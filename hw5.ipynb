# 优化版本：导入更多必要的库
import re
import nltk  
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 文本处理相关
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from nltk.stem import WordNetLemmatizer
from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords

# 聚类算法
from sklearn.cluster import KMeans, AgglomerativeClustering, DBSCAN, SpectralClustering
from sklearn.mixture import GaussianMixture

# 降维算法
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA, LatentDirichletAllocation

# 评估指标
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 下载NLTK数据
try:
    nltk.download('punkt', quiet=True)
    nltk.download('wordnet', quiet=True) 
    nltk.download('punkt_tab', quiet=True)
    nltk.download('stopwords', quiet=True)
except:
    pass


# 读取数据
papers = pd.read_csv('data/[UCI] AAAI-14 Accepted Papers - Papers.csv')
# 打印信息
print(papers.info())
papers.head()

# 初始化文本处理工具
lemmatizer = WordNetLemmatizer()
stop_words = set(stopwords.words('english'))

# 添加学术论文常见的停用词
academic_stopwords = {'paper', 'study', 'research', 'method', 'approach', 
                     'algorithm', 'model', 'result', 'analysis', 'data',
                     'problem', 'solution', 'system', 'work', 'show',
                     'present', 'propose', 'use', 'based', 'new'}
stop_words.update(academic_stopwords)

def preprocess_basic(text):
    """原始预处理方法"""
    text = re.sub(r'\W', ' ', text)
    text = text.lower()
    tokens = word_tokenize(text)
    lemmas = [lemmatizer.lemmatize(token) for token in tokens]
    return " ".join(lemmas)

def preprocess_enhanced(text):
    """增强预处理方法"""
    # 只保留字母，移除数字和特殊字符
    text = re.sub(r'[^a-zA-Z\s]', ' ', text)
    text = text.lower()
    tokens = word_tokenize(text)
    # 过滤停用词、短词和长词
    tokens = [token for token in tokens 
             if token not in stop_words 
             and len(token) > 2 
             and len(token) < 20]
    lemmas = [lemmatizer.lemmatize(token) for token in tokens]
    return " ".join(lemmas)

def create_weighted_text(row, title_weight=3, keywords_weight=2, abstract_weight=1):
    """创建加权文本（标题和关键词权重更高）"""
    title = str(row['title']) if pd.notna(row['title']) else ""
    keywords = str(row['keywords']) if pd.notna(row['keywords']) else ""
    abstract = str(row['abstract']) if pd.notna(row['abstract']) else ""
    
    # 重复文本以增加权重
    weighted_text = (title + " ") * title_weight + \
                   (keywords + " ") * keywords_weight + \
                   (abstract + " ") * abstract_weight
    
    return weighted_text.strip()

# 测试不同的预处理策略
print("测试不同文本预处理策略...")

# 方法1：原始方法
papers['text_basic'] = papers['title'] + ' ' + papers['keywords'] + ' ' + papers['abstract']
papers['processed_basic'] = papers['text_basic'].apply(preprocess_basic)

# 方法2：增强预处理
papers['text_enhanced'] = papers['title'] + ' ' + papers['keywords'] + ' ' + papers['abstract']
papers['processed_enhanced'] = papers['text_enhanced'].apply(preprocess_enhanced)

# 方法3：加权合并
papers['text_weighted'] = papers.apply(create_weighted_text, axis=1)
papers['processed_weighted'] = papers['text_weighted'].apply(preprocess_basic)

# 方法4：加权合并 + 增强预处理
papers['text_weighted_enhanced'] = papers.apply(create_weighted_text, axis=1)
papers['processed_weighted_enhanced'] = papers['text_weighted_enhanced'].apply(preprocess_enhanced)

print("预处理完成！")
papers[['processed_basic', 'processed_enhanced', 'processed_weighted_enhanced']].head()

def vectorize_and_evaluate(texts, method='tfidf', **kwargs):
    """向量化文本并返回矩阵"""
    if method == 'tfidf':
        vectorizer = TfidfVectorizer(
            stop_words='english',
            max_features=kwargs.get('max_features', 2000),
            min_df=kwargs.get('min_df', 2),
            max_df=kwargs.get('max_df', 0.95),
            ngram_range=kwargs.get('ngram_range', (1, 1))
        )
    elif method == 'count':
        vectorizer = CountVectorizer(
            stop_words='english',
            max_features=kwargs.get('max_features', 2000),
            min_df=kwargs.get('min_df', 2),
            max_df=kwargs.get('max_df', 0.95),
            ngram_range=kwargs.get('ngram_range', (1, 1))
        )
    
    matrix = vectorizer.fit_transform(texts)
    return matrix, vectorizer

# 测试不同向量化参数
print("测试不同向量化参数...")

# 使用最优预处理文本进行向量化测试
test_texts = papers['processed_weighted_enhanced']

# 原始参数
matrix_original, vec_original = vectorize_and_evaluate(test_texts, max_features=1000)
print(f"原始参数 - 矩阵形状: {matrix_original.shape}")

# 优化参数1：增加特征数量
matrix_more_features, vec_more = vectorize_and_evaluate(test_texts, max_features=2000, min_df=2, max_df=0.95)
print(f"更多特征 - 矩阵形状: {matrix_more_features.shape}")

# 优化参数2：添加bigram
matrix_bigram, vec_bigram = vectorize_and_evaluate(test_texts, max_features=2000, min_df=2, max_df=0.95, ngram_range=(1, 2))
print(f"包含bigram - 矩阵形状: {matrix_bigram.shape}")

def perform_clustering(matrix, method='kmeans', n_clusters=10, **kwargs):
    """执行聚类并返回标签"""
    if method == 'kmeans':
        clusterer = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        labels = clusterer.fit_predict(matrix.toarray())
        
    elif method == 'hierarchical':
        clusterer = AgglomerativeClustering(n_clusters=n_clusters, linkage='ward')
        labels = clusterer.fit_predict(matrix.toarray())
        
    elif method == 'dbscan':
        eps = kwargs.get('eps', 0.5)
        min_samples = kwargs.get('min_samples', 5)
        clusterer = DBSCAN(eps=eps, min_samples=min_samples)
        labels = clusterer.fit_predict(matrix.toarray())
        
    elif method == 'gmm':
        clusterer = GaussianMixture(n_components=n_clusters, random_state=42)
        labels = clusterer.fit_predict(matrix.toarray())
        
    elif method == 'spectral':
        clusterer = SpectralClustering(n_clusters=n_clusters, random_state=42)
        labels = clusterer.fit_predict(matrix.toarray())
    
    return labels, clusterer

def evaluate_clustering(matrix, labels):
    """评估聚类质量"""
    # 过滤噪声点（标签为-1的点，主要针对DBSCAN）
    valid_mask = labels != -1
    if not np.any(valid_mask):
        return {'silhouette': -1, 'calinski_harabasz': 0, 'davies_bouldin': float('inf')}
    
    valid_matrix = matrix[valid_mask]
    valid_labels = labels[valid_mask]
    
    # 检查是否有足够的簇
    n_clusters = len(set(valid_labels))
    if n_clusters < 2:
        return {'silhouette': -1, 'calinski_harabasz': 0, 'davies_bouldin': float('inf')}
    
    try:
        if hasattr(valid_matrix, 'toarray'):
            valid_matrix = valid_matrix.toarray()
        
        silhouette = silhouette_score(valid_matrix, valid_labels)
        calinski_harabasz = calinski_harabasz_score(valid_matrix, valid_labels)
        davies_bouldin = davies_bouldin_score(valid_matrix, valid_labels)
        
        return {
            'silhouette': silhouette,
            'calinski_harabasz': calinski_harabasz,
            'davies_bouldin': davies_bouldin,
            'n_clusters': n_clusters,
            'n_noise': np.sum(labels == -1)
        }
    except Exception as e:
        print(f"评估聚类时出错：{e}")
        return {'silhouette': -1, 'calinski_harabasz': 0, 'davies_bouldin': float('inf')}

# 使用最优向量化结果进行聚类对比
print("\n对比不同聚类算法...")
clustering_results = {}

clustering_methods = [
    ('kmeans', {'n_clusters': 10}),
    ('hierarchical', {'n_clusters': 10}),
    ('dbscan', {'eps': 0.3, 'min_samples': 3}),
    ('gmm', {'n_clusters': 10}),
    ('spectral', {'n_clusters': 10})
]

for method_name, params in clustering_methods:
    try:
        print(f"\n正在测试聚类方法：{method_name}")
        labels, clusterer = perform_clustering(matrix_more_features, method=method_name, **params)
        scores = evaluate_clustering(matrix_more_features, labels)
        
        clustering_results[method_name] = {
            'labels': labels,
            'scores': scores,
            'clusterer': clusterer
        }
        
        print(f"簇数: {scores.get('n_clusters', 'N/A')}, "
              f"轮廓系数: {scores['silhouette']:.3f}, "
              f"噪声点: {scores.get('n_noise', 0)}")
        
    except Exception as e:
        print(f"聚类方法 {method_name} 执行失败：{e}")
        clustering_results[method_name] = {'error': str(e)}

print("Top terms per cluster:")
order_centroids = kmeans.cluster_centers_.argsort()[:, ::-1]
terms = vectorizer.get_feature_names_out()
for i in range(num_clusters):
    print(f"Cluster {i}:", end='')
    for ind in order_centroids[i, :10]:
        print(f' {terms[ind]}', end='')
    print()

from sklearn.manifold import TSNE
import matplotlib.pyplot as plt
import seaborn as sns

# 使用t-SNE进行降维
tsne = TSNE(n_components=2, random_state=42, perplexity=30, n_iter=300)
tsne_results = tsne.fit_transform(tfidf_matrix.toarray())

# 创建一个包含t-SNE结果和簇标签的DataFrame
tsne_df = pd.DataFrame(tsne_results, columns=['tsne1', 'tsne2'])
tsne_df['cluster'] = papers['cluster']

# 可视化
plt.figure(figsize=(10, 6))
sns.scatterplot(
    x="tsne1", y="tsne2",
    hue="cluster",
    palette=sns.color_palette("hsv", 10),
    data=tsne_df,
    legend="full",
    alpha=0.8
)
plt.title('K-means聚类结果（t-SNE降维）')
plt.show()