# 修复 hw5.ipynb 中的 KeyError: 'cluster' 问题
# 请将此代码复制到您的notebook中，在t-SNE可视化代码之前运行

# 检查是否存在cluster列
if 'cluster' not in papers.columns:
    print("检测到缺少cluster列，正在创建...")
    
    # 确保有tfidf_matrix
    if 'tfidf_matrix' not in locals() and 'tfidf_matrix' not in globals():
        print("重新创建TF-IDF矩阵...")
        from sklearn.feature_extraction.text import TfidfVectorizer
        
        # 如果有processed_text列就用它，否则创建
        if 'processed_text' not in papers.columns:
            papers['text'] = papers['title'] + ' ' + papers['keywords'] + ' ' + papers['abstract']
            papers['processed_text'] = papers['text'].apply(preprocess)
        
        vectorizer = TfidfVectorizer(stop_words='english', max_features=1000)
        tfidf_matrix = vectorizer.fit_transform(papers['processed_text'])
        print(f'TF-IDF matrix shape: {tfidf_matrix.shape}')
    
    # 执行K-means聚类
    print("执行K-means聚类...")
    from sklearn.cluster import KMeans
    
    num_clusters = 10
    kmeans = KMeans(n_clusters=num_clusters, random_state=42, n_init=10)
    kmeans.fit(tfidf_matrix)
    
    # 创建cluster列
    papers['cluster'] = kmeans.labels_
    
    # 显示聚类结果
    print("聚类完成！")
    print("各簇的论文数量：")
    print(papers['cluster'].value_counts().sort_index())
    
    # 显示每个簇的主题词
    print("\n各簇主题词：")
    order_centroids = kmeans.cluster_centers_.argsort()[:, ::-1]
    terms = vectorizer.get_feature_names_out()
    for i in range(num_clusters):
        print(f"簇 {i}:", end='')
        for ind in order_centroids[i, :10]:
            print(f' {terms[ind]}', end='')
        print()

else:
    print("cluster列已存在，可以继续执行t-SNE可视化")

print("\n现在可以安全运行t-SNE可视化代码了！")
