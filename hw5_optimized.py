#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AAAI 2014会议论文聚类分析 - 优化版本
作者：AI助手
日期：2025-01-21

本脚本实现了多种文本预处理、向量化、聚类和降维方法的对比分析
"""

import re
import nltk
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.cluster import KMeans, AgglomerativeClustering, DBSCAN, SpectralClustering
from sklearn.mixture import GaussianMixture
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA, LatentDirichletAllocation
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
from nltk.stem import WordNetLemmatizer
from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 下载必要的NLTK数据
try:
    nltk.download('punkt', quiet=True)
    nltk.download('wordnet', quiet=True)
    nltk.download('punkt_tab', quiet=True)
    nltk.download('stopwords', quiet=True)
except:
    pass

class TextClusteringAnalyzer:
    """文本聚类分析器"""
    
    def __init__(self, data_path):
        """初始化分析器"""
        self.data_path = data_path
        self.papers = None
        self.lemmatizer = WordNetLemmatizer()
        self.stop_words = set(stopwords.words('english'))
        # 添加一些学术论文常见的停用词
        self.stop_words.update(['paper', 'study', 'research', 'method', 'approach', 
                               'algorithm', 'model', 'result', 'analysis', 'data',
                               'problem', 'solution', 'system', 'work', 'show',
                               'present', 'propose', 'use', 'based', 'new'])
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        self.papers = pd.read_csv(self.data_path)
        print(f"数据加载完成，共{len(self.papers)}篇论文")
        return self.papers
    
    def preprocess_text_basic(self, text):
        """基础文本预处理（原始方法）"""
        # 移除非字母数字字符
        text = re.sub(r'\W', ' ', text)
        # 转换为小写
        text = text.lower()
        # 分词
        tokens = word_tokenize(text)
        # 词形还原
        lemmas = [self.lemmatizer.lemmatize(token) for token in tokens]
        return " ".join(lemmas)
    
    def preprocess_text_enhanced(self, text):
        """增强文本预处理"""
        # 移除非字母字符，保留空格
        text = re.sub(r'[^a-zA-Z\s]', ' ', text)
        # 转换为小写
        text = text.lower()
        # 分词
        tokens = word_tokenize(text)
        # 过滤停用词、短词和长词
        tokens = [token for token in tokens 
                 if token not in self.stop_words 
                 and len(token) > 2 
                 and len(token) < 20]
        # 词形还原
        lemmas = [self.lemmatizer.lemmatize(token) for token in tokens]
        return " ".join(lemmas)
    
    def create_weighted_text(self, row, title_weight=3, keywords_weight=2, abstract_weight=1):
        """创建加权文本（标题和关键词权重更高）"""
        title = str(row['title']) if pd.notna(row['title']) else ""
        keywords = str(row['keywords']) if pd.notna(row['keywords']) else ""
        abstract = str(row['abstract']) if pd.notna(row['abstract']) else ""
        
        # 重复文本以增加权重
        weighted_text = (title + " ") * title_weight + \
                       (keywords + " ") * keywords_weight + \
                       (abstract + " ") * abstract_weight
        
        return weighted_text.strip()
    
    def prepare_texts(self, method='enhanced_weighted'):
        """准备文本数据"""
        print(f"正在使用{method}方法准备文本...")
        
        if method == 'basic':
            # 原始方法：简单合并
            self.papers['text'] = (self.papers['title'] + ' ' + 
                                  self.papers['keywords'] + ' ' + 
                                  self.papers['abstract'])
            self.papers['processed_text'] = self.papers['text'].apply(self.preprocess_text_basic)
            
        elif method == 'enhanced':
            # 增强方法：简单合并 + 增强预处理
            self.papers['text'] = (self.papers['title'] + ' ' + 
                                  self.papers['keywords'] + ' ' + 
                                  self.papers['abstract'])
            self.papers['processed_text'] = self.papers['text'].apply(self.preprocess_text_enhanced)
            
        elif method == 'weighted':
            # 加权方法：加权合并 + 基础预处理
            self.papers['text'] = self.papers.apply(self.create_weighted_text, axis=1)
            self.papers['processed_text'] = self.papers['text'].apply(self.preprocess_text_basic)
            
        elif method == 'enhanced_weighted':
            # 最优方法：加权合并 + 增强预处理
            self.papers['text'] = self.papers.apply(self.create_weighted_text, axis=1)
            self.papers['processed_text'] = self.papers['text'].apply(self.preprocess_text_enhanced)
        
        print(f"文本预处理完成，方法：{method}")
        return self.papers['processed_text']

    def vectorize_texts(self, texts, method='tfidf', **kwargs):
        """文本向量化"""
        print(f"正在使用{method}方法进行向量化...")

        if method == 'tfidf':
            # TF-IDF向量化
            max_features = kwargs.get('max_features', 1000)
            min_df = kwargs.get('min_df', 2)
            max_df = kwargs.get('max_df', 0.95)
            ngram_range = kwargs.get('ngram_range', (1, 1))

            vectorizer = TfidfVectorizer(
                stop_words='english',
                max_features=max_features,
                min_df=min_df,
                max_df=max_df,
                ngram_range=ngram_range
            )

        elif method == 'count':
            # 词频向量化
            max_features = kwargs.get('max_features', 1000)
            min_df = kwargs.get('min_df', 2)
            max_df = kwargs.get('max_df', 0.95)
            ngram_range = kwargs.get('ngram_range', (1, 1))

            vectorizer = CountVectorizer(
                stop_words='english',
                max_features=max_features,
                min_df=min_df,
                max_df=max_df,
                ngram_range=ngram_range
            )

        # 拟合并转换
        matrix = vectorizer.fit_transform(texts)
        print(f"向量化完成，矩阵形状：{matrix.shape}")

        return matrix, vectorizer

    def perform_clustering(self, matrix, method='kmeans', n_clusters=10, **kwargs):
        """执行聚类"""
        print(f"正在使用{method}方法进行聚类...")

        if method == 'kmeans':
            clusterer = KMeans(
                n_clusters=n_clusters,
                random_state=42,
                n_init=10,
                **kwargs
            )

        elif method == 'hierarchical':
            clusterer = AgglomerativeClustering(
                n_clusters=n_clusters,
                linkage='ward',
                **kwargs
            )

        elif method == 'dbscan':
            eps = kwargs.get('eps', 0.5)
            min_samples = kwargs.get('min_samples', 5)
            clusterer = DBSCAN(eps=eps, min_samples=min_samples)

        elif method == 'gmm':
            clusterer = GaussianMixture(
                n_components=n_clusters,
                random_state=42,
                **kwargs
            )

        elif method == 'spectral':
            clusterer = SpectralClustering(
                n_clusters=n_clusters,
                random_state=42,
                **kwargs
            )

        # 执行聚类
        if method == 'dbscan':
            labels = clusterer.fit_predict(matrix.toarray())
        elif method == 'gmm':
            labels = clusterer.fit_predict(matrix.toarray())
        else:
            if hasattr(matrix, 'toarray'):
                labels = clusterer.fit_predict(matrix.toarray())
            else:
                labels = clusterer.fit_predict(matrix)

        print(f"聚类完成，发现{len(set(labels))}个簇")
        return labels, clusterer

    def reduce_dimensions(self, matrix, method='tsne', n_components=2, **kwargs):
        """降维"""
        print(f"正在使用{method}方法进行降维...")

        if method == 'tsne':
            perplexity = kwargs.get('perplexity', 30)
            n_iter = kwargs.get('n_iter', 300)
            reducer = TSNE(
                n_components=n_components,
                random_state=42,
                perplexity=perplexity,
                n_iter=n_iter
            )

        elif method == 'pca':
            reducer = PCA(n_components=n_components, random_state=42)

        elif method == 'lda':
            n_topics = kwargs.get('n_topics', 10)
            reducer = LatentDirichletAllocation(
                n_components=n_topics,
                random_state=42,
                max_iter=10
            )

        # 执行降维
        if hasattr(matrix, 'toarray'):
            reduced_data = reducer.fit_transform(matrix.toarray())
        else:
            reduced_data = reducer.fit_transform(matrix)

        print(f"降维完成，新维度：{reduced_data.shape}")
        return reduced_data, reducer

    def evaluate_clustering(self, matrix, labels):
        """评估聚类质量"""
        # 过滤噪声点（标签为-1的点，主要针对DBSCAN）
        valid_mask = labels != -1
        if not np.any(valid_mask):
            return {'silhouette': -1, 'calinski_harabasz': 0, 'davies_bouldin': float('inf')}

        valid_matrix = matrix[valid_mask]
        valid_labels = labels[valid_mask]

        # 检查是否有足够的簇
        n_clusters = len(set(valid_labels))
        if n_clusters < 2:
            return {'silhouette': -1, 'calinski_harabasz': 0, 'davies_bouldin': float('inf')}

        try:
            if hasattr(valid_matrix, 'toarray'):
                valid_matrix = valid_matrix.toarray()

            silhouette = silhouette_score(valid_matrix, valid_labels)
            calinski_harabasz = calinski_harabasz_score(valid_matrix, valid_labels)
            davies_bouldin = davies_bouldin_score(valid_matrix, valid_labels)

            return {
                'silhouette': silhouette,
                'calinski_harabasz': calinski_harabasz,
                'davies_bouldin': davies_bouldin,
                'n_clusters': n_clusters,
                'n_noise': np.sum(labels == -1)
            }
        except Exception as e:
            print(f"评估聚类时出错：{e}")
            return {'silhouette': -1, 'calinski_harabasz': 0, 'davies_bouldin': float('inf')}

    def visualize_clusters(self, reduced_data, labels, title="聚类可视化", method_info=""):
        """可视化聚类结果"""
        plt.figure(figsize=(12, 8))

        # 获取唯一标签
        unique_labels = set(labels)
        n_clusters = len(unique_labels)

        # 为噪声点（标签-1）设置特殊颜色
        colors = plt.cm.Set3(np.linspace(0, 1, max(n_clusters, 10)))

        for i, label in enumerate(unique_labels):
            if label == -1:
                # 噪声点用黑色
                color = 'black'
                marker = 'x'
                alpha = 0.5
                label_name = '噪声点'
            else:
                color = colors[i % len(colors)]
                marker = 'o'
                alpha = 0.7
                label_name = f'簇 {label}'

            mask = labels == label
            plt.scatter(
                reduced_data[mask, 0],
                reduced_data[mask, 1],
                c=[color],
                marker=marker,
                alpha=alpha,
                s=50,
                label=label_name
            )

        plt.title(f'{title}\n{method_info}', fontsize=14)
        plt.xlabel('维度 1')
        plt.ylabel('维度 2')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.tight_layout()
        plt.show()

        return plt.gcf()

    def analyze_cluster_topics(self, vectorizer, clusterer, labels, n_terms=10):
        """分析每个簇的主题词"""
        if not hasattr(clusterer, 'cluster_centers_'):
            print("该聚类方法不支持簇中心分析")
            return

        print("\n各簇主题词分析：")
        print("=" * 50)

        terms = vectorizer.get_feature_names_out()
        order_centroids = clusterer.cluster_centers_.argsort()[:, ::-1]

        for i in range(len(clusterer.cluster_centers_)):
            cluster_size = np.sum(labels == i)
            print(f"\n簇 {i} (包含 {cluster_size} 篇论文):")
            top_terms = [terms[ind] for ind in order_centroids[i, :n_terms]]
            print(f"主题词: {', '.join(top_terms)}")

    def compare_preprocessing_methods(self):
        """对比不同预处理方法"""
        print("\n" + "="*60)
        print("对比不同文本预处理方法")
        print("="*60)

        methods = ['basic', 'enhanced', 'weighted', 'enhanced_weighted']
        results = {}

        for method in methods:
            print(f"\n正在测试预处理方法：{method}")

            # 准备文本
            texts = self.prepare_texts(method=method)

            # 向量化
            matrix, vectorizer = self.vectorize_texts(texts, method='tfidf', max_features=1000)

            # K-means聚类
            labels, clusterer = self.perform_clustering(matrix, method='kmeans', n_clusters=10)

            # 评估
            scores = self.evaluate_clustering(matrix, labels)

            # t-SNE降维可视化
            reduced_data, _ = self.reduce_dimensions(matrix, method='tsne')

            results[method] = {
                'scores': scores,
                'labels': labels,
                'reduced_data': reduced_data,
                'vectorizer': vectorizer,
                'clusterer': clusterer
            }

            # 可视化
            self.visualize_clusters(
                reduced_data, labels,
                title=f"预处理方法对比 - {method}",
                method_info=f"轮廓系数: {scores['silhouette']:.3f}"
            )

        return results

    def compare_clustering_methods(self, texts):
        """对比不同聚类方法"""
        print("\n" + "="*60)
        print("对比不同聚类方法")
        print("="*60)

        # 使用最优预处理方法
        matrix, vectorizer = self.vectorize_texts(texts, method='tfidf', max_features=1000)

        clustering_methods = [
            ('kmeans', {'n_clusters': 10}),
            ('hierarchical', {'n_clusters': 10}),
            ('dbscan', {'eps': 0.5, 'min_samples': 5}),
            ('gmm', {'n_clusters': 10}),
            ('spectral', {'n_clusters': 10})
        ]

        results = {}

        for method_name, params in clustering_methods:
            print(f"\n正在测试聚类方法：{method_name}")

            try:
                # 聚类
                labels, clusterer = self.perform_clustering(matrix, method=method_name, **params)

                # 评估
                scores = self.evaluate_clustering(matrix, labels)

                # t-SNE降维可视化
                reduced_data, _ = self.reduce_dimensions(matrix, method='tsne')

                results[method_name] = {
                    'scores': scores,
                    'labels': labels,
                    'reduced_data': reduced_data,
                    'clusterer': clusterer
                }

                # 可视化
                self.visualize_clusters(
                    reduced_data, labels,
                    title=f"聚类方法对比 - {method_name}",
                    method_info=f"轮廓系数: {scores['silhouette']:.3f}, 簇数: {scores.get('n_clusters', 'N/A')}"
                )

                # 分析主题词（如果支持）
                if hasattr(clusterer, 'cluster_centers_'):
                    self.analyze_cluster_topics(vectorizer, clusterer, labels)

            except Exception as e:
                print(f"聚类方法 {method_name} 执行失败：{e}")
                results[method_name] = {'error': str(e)}

        return results

    def compare_dimensionality_reduction(self, matrix, labels):
        """对比不同降维方法"""
        print("\n" + "="*60)
        print("对比不同降维方法")
        print("="*60)

        reduction_methods = [
            ('tsne', {'perplexity': 30, 'n_iter': 300}),
            ('pca', {}),
        ]

        results = {}

        for method_name, params in reduction_methods:
            print(f"\n正在测试降维方法：{method_name}")

            try:
                # 降维
                reduced_data, reducer = self.reduce_dimensions(matrix, method=method_name, **params)

                results[method_name] = {
                    'reduced_data': reduced_data,
                    'reducer': reducer
                }

                # 可视化
                self.visualize_clusters(
                    reduced_data, labels,
                    title=f"降维方法对比 - {method_name}",
                    method_info=f"降维方法: {method_name.upper()}"
                )

            except Exception as e:
                print(f"降维方法 {method_name} 执行失败：{e}")
                results[method_name] = {'error': str(e)}

        return results

    def generate_summary_report(self, preprocessing_results, clustering_results, reduction_results):
        """生成总结报告"""
        print("\n" + "="*80)
        print("实验总结报告")
        print("="*80)

        # 预处理方法对比
        print("\n1. 文本预处理方法对比:")
        print("-" * 40)
        for method, result in preprocessing_results.items():
            scores = result['scores']
            print(f"{method:20s}: 轮廓系数={scores['silhouette']:.3f}, "
                  f"CH指数={scores['calinski_harabasz']:.1f}, "
                  f"DB指数={scores['davies_bouldin']:.3f}")

        # 找出最佳预处理方法
        best_preprocessing = max(preprocessing_results.keys(),
                               key=lambda x: preprocessing_results[x]['scores']['silhouette'])
        print(f"\n最佳预处理方法: {best_preprocessing}")

        # 聚类方法对比
        print("\n2. 聚类方法对比:")
        print("-" * 40)
        for method, result in clustering_results.items():
            if 'error' in result:
                print(f"{method:20s}: 执行失败 - {result['error']}")
            else:
                scores = result['scores']
                print(f"{method:20s}: 轮廓系数={scores['silhouette']:.3f}, "
                      f"簇数={scores.get('n_clusters', 'N/A')}, "
                      f"噪声点={scores.get('n_noise', 0)}")

        # 找出最佳聚类方法
        valid_clustering = {k: v for k, v in clustering_results.items() if 'error' not in v}
        if valid_clustering:
            best_clustering = max(valid_clustering.keys(),
                                key=lambda x: valid_clustering[x]['scores']['silhouette'])
            print(f"\n最佳聚类方法: {best_clustering}")

        print("\n3. 降维方法对比:")
        print("-" * 40)
        for method in reduction_results.keys():
            if 'error' in reduction_results[method]:
                print(f"{method:20s}: 执行失败")
            else:
                print(f"{method:20s}: 执行成功")

        print("\n4. 改进建议:")
        print("-" * 40)
        print("• 使用加权文本合并策略，提高标题和关键词的权重")
        print("• 增强停用词过滤，移除学术论文常见的无意义词汇")
        print("• 尝试不同的向量化参数组合")
        print("• 根据数据特点选择合适的聚类算法")
        print("• 使用多种降维方法验证聚类结果的稳定性")


def main():
    """主函数"""
    print("AAAI 2014会议论文聚类分析 - 优化版本")
    print("="*60)

    # 初始化分析器
    analyzer = TextClusteringAnalyzer('data/[UCI] AAAI-14 Accepted Papers - Papers.csv')

    # 加载数据
    analyzer.load_data()

    # 1. 对比不同预处理方法
    preprocessing_results = analyzer.compare_preprocessing_methods()

    # 2. 使用最佳预处理方法进行后续分析
    best_method = max(preprocessing_results.keys(),
                     key=lambda x: preprocessing_results[x]['scores']['silhouette'])
    print(f"\n选择最佳预处理方法: {best_method}")

    # 准备最优文本
    best_texts = analyzer.prepare_texts(method=best_method)

    # 3. 对比不同聚类方法
    clustering_results = analyzer.compare_clustering_methods(best_texts)

    # 4. 对比不同降维方法（使用最佳聚类结果）
    valid_clustering = {k: v for k, v in clustering_results.items() if 'error' not in v}
    if valid_clustering:
        best_clustering_method = max(valid_clustering.keys(),
                                   key=lambda x: valid_clustering[x]['scores']['silhouette'])
        best_labels = clustering_results[best_clustering_method]['labels']

        # 重新获取向量矩阵
        matrix, _ = analyzer.vectorize_texts(best_texts, method='tfidf', max_features=1000)
        reduction_results = analyzer.compare_dimensionality_reduction(matrix, best_labels)
    else:
        reduction_results = {}

    # 5. 生成总结报告
    analyzer.generate_summary_report(preprocessing_results, clustering_results, reduction_results)

    print("\n分析完成！")


if __name__ == "__main__":
    main()
