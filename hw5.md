# AAAI 2014 会议论文聚类分析

本次实验旨在对AAAI 2014会议的论文进行无监督聚类分析。我们利用论文的标题、关键词和摘要信息，通过自然语言处理技术和机器学习算法，将相似的论文划分到同一簇中，并对聚类结果进行可视化和解读。

## 1. 数据加载与预处理

### 1.1 数据加载

首先，我们加载了包含AAAI 2014会议论文信息的数据集。该数据集为一个CSV文件，包含了每篇论文的标题、作者、关键词、摘要、group和topic等信息。

```python
import pandas as pd

# 读取数据
papers = pd.read_csv('data/[UCI] AAAI-14 Accepted Papers - Papers.csv')
# 打印信息
print(papers.info())
papers.head()
```

### 1.2 文本合并与清洗

为了对论文进行聚类，我们将每篇论文的**标题(title)**、**关键词(keywords)**和**摘要(abstract)**合并成一个单独的文本字段`text`。这样做可以综合利用这三部分信息，更全面地表征论文内容。

接下来，我们定义了一个`preprocess`函数来对合并后的文本进行清洗和标准化。该函数主要执行以下操作：

1.  **移除非字母数字字符**：使用正则表达式去除所有标点符号和特殊字符。
2.  **转换为小写**：将所有文本转换为小写，以避免大小写导致的词汇不一致。
3.  **分词 (Tokenization)**：将文本切分成单词（token）。
4.  **词形还原 (Lemmatization)**：将单词还原为其基本形式（lemma），例如将 "running" 还原为 "run"。这有助于减少词汇的冗余。

```python
from nltk.stem import WordNetLemmatizer
from nltk.tokenize import word_tokenize
import re

# 提取标题、关键词和摘要
papers['text'] = papers['title'] + ' ' + papers['keywords'] + ' ' + papers['abstract']

lemmatizer = WordNetLemmatizer()

def preprocess(text):
    # 移除非字母数字字符
    text = re.sub(r'\W', ' ', text)
    # 转换为小写
    text = text.lower()
    # 分词
    tokens = word_tokenize(text)
    # 词形还原
    lemmas = [lemmatizer.lemmatize(token) for token in tokens]
    return " ".join(lemmas)

papers['processed_text'] = papers['text'].apply(preprocess)
```

## 2. 文本向量化 (TF-IDF)

计算机无法直接处理文本数据，因此我们需要将预处理后的文本转换成数值向量。这里我们采用了**TF-IDF (Term Frequency-Inverse Document Frequency)**方法。TF-IDF是一种常用的文本表示技术，它能够评估一个词对于一篇文档的重要性。

我们使用`sklearn`的`TfidfVectorizer`来创建TF-IDF矩阵，并设置`stop_words='english'`来移除常见的英文停用词（如 "a", "the", "is"），同时通过`max_features=1000`限制了特征向量的维度为1000，只保留最重要的1000个词。

```python
from sklearn.feature_extraction.text import TfidfVectorizer

# 创建TF-IDF向量化器
vectorizer = TfidfVectorizer(stop_words='english', max_features=1000)

# 拟合并转换数据
tfidf_matrix = vectorizer.fit_transform(papers['processed_text'])

print(f'TF-IDF matrix shape: {tfidf_matrix.shape}')
```

## 3. K-Means 聚类

在得到论文的TF-IDF向量表示后，我们使用**K-Means算法**对论文进行聚类。K-Means是一种经典的无监督聚类算法，它试图将数据分成K个簇，使得每个数据点都属于离它最近的均值（簇中心）对应的簇。

我们将簇的数量`num_clusters`设置为10，并使用`random_state`来保证结果的可复现性。

```python
from sklearn.cluster import KMeans

num_clusters = 10
kmeans = KMeans(n_clusters=num_clusters, random_state=42, n_init=10)
kmeans.fit(tfidf_matrix)

papers['cluster'] = kmeans.labels_
```

为了解每个簇的主题，我们打印出了每个簇中TF-IDF值最高的10个中心词。这些词可以很好地概括该簇内论文的主要内容。

```python
print("Top terms per cluster:")
order_centroids = kmeans.cluster_centers_.argsort()[:, ::-1]
terms = vectorizer.get_feature_names_out()
for i in range(num_clusters):
    print(f"Cluster {i}:", end='')
    for ind in order_centroids[i, :10]:
        print(f' {terms[ind]}', end='')
    print()
```

## 4. 降维与可视化 (t-SNE)

为了直观地观察聚类效果，我们将高维的TF-IDF向量降维到二维空间并进行可视化。这里我们使用了**t-SNE (t-Distributed Stochastic Neighbor Embedding)**算法，这是一种非常适合高维数据可视化的降维技术。

我们将降维后的二维坐标和聚类标签存储在一个DataFrame中，并使用`seaborn`和`matplotlib`绘制散点图。图中的每一个点代表一篇论文，点的颜色代表其所属的簇。

```python
from sklearn.manifold import TSNE
import matplotlib.pyplot as plt
import seaborn as sns

# 使用t-SNE进行降维
tsne = TSNE(n_components=2, random_state=42, perplexity=30, n_iter=300)
tsne_results = tsne.fit_transform(tfidf_matrix.toarray())

# 创建一个包含t-SNE结果和簇标签的DataFrame
tsne_df = pd.DataFrame(tsne_results, columns=['tsne1', 'tsne2'])
tsne_df['cluster'] = papers['cluster']

# 可视化
plt.figure(figsize=(12, 8))
sns.scatterplot(
    x="tsne1", y="tsne2",
    hue="cluster",
    palette=sns.color_palette("hsv", 10),
    data=tsne_df,
    legend="full",
    alpha=0.8
)
plt.title('t-SNE visualization of paper clusters')
plt.show()
```

通过t-SNE可视化图，我们可以看到不同簇的论文在二维空间中的分布情况。尽管存在一些重叠，但总体上还是形成了一些可区分的聚类，说明我们的聚类方法在一定程度上捕捉到了论文的主题结构。
