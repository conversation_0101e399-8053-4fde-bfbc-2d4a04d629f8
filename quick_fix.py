# 快速修复KeyError的代码
# 请在您的notebook中运行这段代码

# 如果papers中没有cluster列，创建一个
if 'cluster' not in papers.columns:
    print("创建聚类标签...")
    
    # 使用现有的tfidf_matrix进行K-means聚类
    from sklearn.cluster import KMeans
    
    kmeans = KMeans(n_clusters=10, random_state=42, n_init=10)
    papers['cluster'] = kmeans.fit_predict(tfidf_matrix)
    
    print("聚类完成！")
    print("各簇大小：")
    print(papers['cluster'].value_counts().sort_index())

# 现在可以安全运行原来的可视化代码
print("现在可以运行t-SNE可视化代码了！")
