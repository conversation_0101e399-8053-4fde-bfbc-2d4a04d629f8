# 修复聚类可视化问题的代码
# 请在notebook中运行这段代码来修复KeyError

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.manifold import TSNE
from sklearn.metrics import silhouette_score

# 如果还没有运行过聚类，先运行一个简单的K-means聚类
if 'cluster' not in papers.columns:
    print("正在创建聚类标签...")
    
    # 使用增强预处理的文本
    if 'processed_weighted_enhanced' in papers.columns:
        texts = papers['processed_weighted_enhanced']
    elif 'processed_text' in papers.columns:
        texts = papers['processed_text']
    else:
        # 如果没有预处理文本，创建一个简单的
        papers['text'] = papers['title'] + ' ' + papers['keywords'] + ' ' + papers['abstract']
        texts = papers['text']
    
    # 向量化
    vectorizer = TfidfVectorizer(stop_words='english', max_features=1000)
    tfidf_matrix = vectorizer.fit_transform(texts)
    
    # K-means聚类
    kmeans = KMeans(n_clusters=10, random_state=42, n_init=10)
    papers['cluster'] = kmeans.fit_predict(tfidf_matrix)
    
    print(f"聚类完成！各簇大小：")
    print(papers['cluster'].value_counts().sort_index())
    
    # 计算轮廓系数
    silhouette = silhouette_score(tfidf_matrix, papers['cluster'])
    print(f"轮廓系数: {silhouette:.3f}")

# 现在可以安全地创建t-SNE可视化
print("\n正在进行t-SNE降维...")
if 'tfidf_matrix' not in locals():
    # 如果没有tfidf_matrix，重新创建
    if 'processed_weighted_enhanced' in papers.columns:
        texts = papers['processed_weighted_enhanced']
    elif 'processed_text' in papers.columns:
        texts = papers['processed_text']
    else:
        texts = papers['text']
    
    vectorizer = TfidfVectorizer(stop_words='english', max_features=1000)
    tfidf_matrix = vectorizer.fit_transform(texts)

# t-SNE降维
tsne = TSNE(n_components=2, random_state=42, perplexity=30, max_iter=300)
tsne_results = tsne.fit_transform(tfidf_matrix.toarray())

# 创建DataFrame
tsne_df = pd.DataFrame(tsne_results, columns=['tsne1', 'tsne2'])
tsne_df['cluster'] = papers['cluster']

# 可视化
plt.figure(figsize=(12, 8))
colors = plt.cm.Set3(np.linspace(0, 1, 10))

for i in range(10):
    mask = tsne_df['cluster'] == i
    plt.scatter(
        tsne_df[mask]['tsne1'], 
        tsne_df[mask]['tsne2'],
        c=[colors[i]], 
        label=f'簇 {i}',
        alpha=0.7,
        s=50
    )

plt.title('t-SNE聚类可视化（修复版）', fontsize=14)
plt.xlabel('t-SNE 维度 1')
plt.ylabel('t-SNE 维度 2')
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
plt.tight_layout()
plt.show()

print("可视化完成！")
