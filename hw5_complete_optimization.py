#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AAAI 2014会议论文聚类分析 - 完整优化版本
解决所有问题并提供完整的对比分析

请将此代码分段复制到您的notebook中运行
"""

# ============================================================================
# 第1部分：导入库和数据加载
# ============================================================================

import re
import nltk
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 文本处理
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from nltk.stem import WordNetLemmatizer
from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords

# 聚类算法
from sklearn.cluster import KMeans, AgglomerativeClustering, DBSCAN, SpectralClustering
from sklearn.mixture import GaussianMixture

# 降维算法
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA

# 评估指标
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 下载NLTK数据
try:
    nltk.download('punkt', quiet=True)
    nltk.download('wordnet', quiet=True)
    nltk.download('punkt_tab', quiet=True)
    nltk.download('stopwords', quiet=True)
except:
    pass

# 加载数据
papers = pd.read_csv('data/[UCI] AAAI-14 Accepted Papers - Papers.csv')
print(f"数据加载完成，共{len(papers)}篇论文")
print(papers.info())

# ============================================================================
# 第2部分：优化的文本预处理
# ============================================================================

print("\n" + "="*60)
print("第1步：优化文本预处理")
print("="*60)

# 初始化工具
lemmatizer = WordNetLemmatizer()
stop_words = set(stopwords.words('english'))

# 添加学术论文特有停用词
academic_stopwords = {
    'paper', 'study', 'research', 'method', 'approach', 'algorithm', 
    'model', 'result', 'analysis', 'data', 'problem', 'solution', 
    'system', 'work', 'show', 'present', 'propose', 'use', 'based', 
    'new', 'using', 'used', 'also', 'one', 'two', 'first', 'second'
}
stop_words.update(academic_stopwords)

def preprocess_enhanced(text):
    """增强的文本预处理"""
    if pd.isna(text):
        return ""
    
    # 只保留字母
    text = re.sub(r'[^a-zA-Z\s]', ' ', str(text))
    text = text.lower()
    
    # 分词
    tokens = word_tokenize(text)
    
    # 过滤停用词、短词和长词
    tokens = [token for token in tokens 
             if token not in stop_words 
             and len(token) > 2 
             and len(token) < 20]
    
    # 词形还原
    lemmas = [lemmatizer.lemmatize(token) for token in tokens]
    return " ".join(lemmas)

def create_weighted_text(row, title_weight=3, keywords_weight=2, abstract_weight=1):
    """创建加权文本"""
    title = str(row['title']) if pd.notna(row['title']) else ""
    keywords = str(row['keywords']) if pd.notna(row['keywords']) else ""
    abstract = str(row['abstract']) if pd.notna(row['abstract']) else ""
    
    # 重复重要部分以增加权重
    weighted_text = (title + " ") * title_weight + \
                   (keywords + " ") * keywords_weight + \
                   (abstract + " ") * abstract_weight
    
    return weighted_text.strip()

# 创建加权文本并预处理
print("创建加权文本并进行增强预处理...")
papers['weighted_text'] = papers.apply(create_weighted_text, axis=1)
papers['processed_text'] = papers['weighted_text'].apply(preprocess_enhanced)

print("预处理完成！")
print(f"处理后的文本示例：")
print(papers['processed_text'].iloc[0][:200] + "...")

# ============================================================================
# 第3部分：优化的文本向量化
# ============================================================================

print("\n" + "="*60)
print("第2步：优化文本向量化")
print("="*60)

# 使用优化的TF-IDF参数
vectorizer = TfidfVectorizer(
    stop_words='english',
    max_features=2000,  # 增加特征数量
    min_df=2,          # 至少出现在2个文档中
    max_df=0.95,       # 最多出现在95%的文档中
    ngram_range=(1, 2) # 包含unigram和bigram
)

tfidf_matrix = vectorizer.fit_transform(papers['processed_text'])
print(f"TF-IDF矩阵形状: {tfidf_matrix.shape}")

# ============================================================================
# 第4部分：多种聚类算法对比
# ============================================================================

print("\n" + "="*60)
print("第3步：多种聚类算法对比")
print("="*60)

def evaluate_clustering(matrix, labels):
    """评估聚类质量"""
    # 过滤噪声点
    valid_mask = labels != -1
    if not np.any(valid_mask) or len(set(labels[valid_mask])) < 2:
        return {'silhouette': -1, 'calinski_harabasz': 0, 'davies_bouldin': float('inf')}
    
    valid_matrix = matrix[valid_mask]
    valid_labels = labels[valid_mask]
    
    try:
        if hasattr(valid_matrix, 'toarray'):
            valid_matrix = valid_matrix.toarray()
        
        silhouette = silhouette_score(valid_matrix, valid_labels)
        calinski_harabasz = calinski_harabasz_score(valid_matrix, valid_labels)
        davies_bouldin = davies_bouldin_score(valid_matrix, valid_labels)
        
        return {
            'silhouette': silhouette,
            'calinski_harabasz': calinski_harabasz,
            'davies_bouldin': davies_bouldin,
            'n_clusters': len(set(valid_labels)),
            'n_noise': np.sum(labels == -1)
        }
    except:
        return {'silhouette': -1, 'calinski_harabasz': 0, 'davies_bouldin': float('inf')}

# 定义聚类方法
clustering_methods = {
    'K-means': KMeans(n_clusters=10, random_state=42, n_init=10),
    'Hierarchical': AgglomerativeClustering(n_clusters=10, linkage='ward'),
    'DBSCAN': DBSCAN(eps=0.3, min_samples=3),
    'GMM': GaussianMixture(n_components=10, random_state=42),
    'Spectral': SpectralClustering(n_clusters=10, random_state=42)
}

clustering_results = {}

for name, clusterer in clustering_methods.items():
    print(f"\n正在测试: {name}")
    try:
        if name == 'DBSCAN' or name == 'GMM':
            labels = clusterer.fit_predict(tfidf_matrix.toarray())
        else:
            labels = clusterer.fit_predict(tfidf_matrix.toarray())
        
        scores = evaluate_clustering(tfidf_matrix, labels)
        clustering_results[name] = {
            'labels': labels,
            'scores': scores,
            'clusterer': clusterer
        }
        
        print(f"  簇数: {scores.get('n_clusters', 'N/A')}")
        print(f"  轮廓系数: {scores['silhouette']:.3f}")
        print(f"  噪声点: {scores.get('n_noise', 0)}")
        
    except Exception as e:
        print(f"  执行失败: {e}")
        clustering_results[name] = {'error': str(e)}

# 选择最佳聚类方法
valid_results = {k: v for k, v in clustering_results.items() if 'error' not in v}
if valid_results:
    best_method = max(valid_results.keys(), 
                     key=lambda x: valid_results[x]['scores']['silhouette'])
    best_labels = clustering_results[best_method]['labels']
    papers['cluster'] = best_labels
    
    print(f"\n最佳聚类方法: {best_method}")
    print(f"轮廓系数: {clustering_results[best_method]['scores']['silhouette']:.3f}")
else:
    # 如果所有方法都失败，使用简单的K-means
    print("\n使用备用K-means聚类...")
    kmeans = KMeans(n_clusters=10, random_state=42)
    papers['cluster'] = kmeans.fit_predict(tfidf_matrix.toarray())
    best_method = "K-means (备用)"

print(f"\n各簇大小分布:")
print(papers['cluster'].value_counts().sort_index())

# ============================================================================
# 第5部分：多种降维方法对比和可视化
# ============================================================================

print("\n" + "="*60)
print("第4步：降维方法对比和可视化")
print("="*60)

# t-SNE降维
print("执行t-SNE降维...")
tsne = TSNE(n_components=2, random_state=42, perplexity=30, max_iter=300)
tsne_results = tsne.fit_transform(tfidf_matrix.toarray())

# PCA降维
print("执行PCA降维...")
pca = PCA(n_components=2, random_state=42)
pca_results = pca.fit_transform(tfidf_matrix.toarray())

# 可视化函数
def plot_clusters(reduced_data, labels, title, method_info=""):
    plt.figure(figsize=(12, 8))
    
    unique_labels = set(labels)
    colors = plt.cm.Set3(np.linspace(0, 1, max(len(unique_labels), 10)))
    
    for i, label in enumerate(unique_labels):
        if label == -1:
            color = 'black'
            marker = 'x'
            alpha = 0.5
            label_name = '噪声点'
        else:
            color = colors[i % len(colors)]
            marker = 'o'
            alpha = 0.7
            label_name = f'簇 {label}'
        
        mask = labels == label
        plt.scatter(
            reduced_data[mask, 0], 
            reduced_data[mask, 1],
            c=[color], 
            marker=marker,
            alpha=alpha,
            s=50,
            label=label_name
        )
    
    plt.title(f'{title}\n{method_info}', fontsize=14)
    plt.xlabel('维度 1')
    plt.ylabel('维度 2')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout()
    plt.show()

# 绘制t-SNE可视化
plot_clusters(
    tsne_results, 
    papers['cluster'],
    f"t-SNE可视化 - {best_method}",
    f"聚类方法: {best_method}"
)

# 绘制PCA可视化
explained_variance = pca.explained_variance_ratio_.sum()
plot_clusters(
    pca_results, 
    papers['cluster'],
    f"PCA可视化 - {best_method}",
    f"解释方差比: {explained_variance:.3f}"
)

print("可视化完成！")
