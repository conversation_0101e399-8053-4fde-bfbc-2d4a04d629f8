{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["本次实验以AAAI 2014会议论文数据为基础，要求实现或调用无监督聚类算法，了解聚类方法。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 任务介绍\n", "每年国际上召开的大大小小学术会议不计其数，发表了非常多的论文。在计算机领域的一些大型学术会议上，一次就可以发表涉及各个方向的几百篇论文。按论文的主题、内容进行聚类，有助于人们高效地查找和获得所需要的论文。本案例数据来源于AAAI 2014上发表的约400篇文章，由[UCI](https://archive.ics.uci.edu/ml/datasets/AAAI+2014+Accepted+Papers!)公开提供，提供包括标题、作者、关键词、摘要在内的信息，希望大家能根据这些信息，合理地构造特征向量来表示这些论文，并设计实现或调用聚类算法对论文进行聚类。最后也可以对聚类结果进行观察，看每一类都是什么样的论文，是否有一些主题。\n", "\n", "基本要求：\n", "1. 将文本转化为向量，实现或调用无监督聚类算法，对论文聚类，例如10类（可使用已有工具包例如sklearn）；\n", "2. 观察每一类中的论文，调整算法使结果较为合理；\n", "3. 无监督聚类没有标签，效果较难评价，因此没有硬性指标，跑通即可，主要让大家了解和感受聚类算法，比较简单。\n", "\n", "扩展要求：\n", "1. 对文本向量进行降维，并将聚类结果可视化成散点图。\n", "\n", "注：group和topic也不能完全算是标签，因为\n", "1. 有些文章作者投稿时可能会选择某个group/topic但实际和另外group/topic也相关甚至更相关；\n", "2. 一篇文章可能有多个group和topic，作为标签会出现有的文章同属多个类别，这里暂不考虑这样的聚类；\n", "3. group和topic的取值很多，但聚类常常希望指定聚合成出例如5/10/20类；\n", "4. 感兴趣但同学可以思考利用group和topic信息来量化评价无监督聚类结果，不作要求。\n", "\n", "提示：\n", "1. 高维向量的降维旨在去除一些高相关性的特征维度，保留最有用的信息，用更低维的向量表示高维数据，常用的方法有PCA和t-SNE等；\n", "2. 降维与聚类是两件不同的事情，聚类实际上在降维前的高维向量和降维后的低维向量上都可以进行，结果也可能截然不同；\n", "3. 高维向量做聚类，降维可视化后若有同一类的点不在一起，是正常的。在高维空间中它们可能是在一起的，降维后损失了一些信息。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# 导入工具包"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n", "[nltk_data] Downloading package wordnet to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Package wordnet is already up-to-date!\n", "[nltk_data] Downloading package punkt_tab to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Package punkt_tab is already up-to-date!\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["import re\n", "import nltk  \n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.feature_extraction.text import TfidfTransformer  # tf-idf文本向量化\n", "from sklearn.cluster import KMeans, AgglomerativeClustering   # 取类方法\n", "from sklearn.manifold import TSNE  # tSNE降维\n", "from sklearn.decomposition import PCA  #\n", "from scipy import sparse  # 稀疏矩阵\n", "import seaborn as sns  # 作图\n", "from collections import Counter\n", "from matplotlib import rcParams\n", "# 设置中文字体为 'Microsoft YaHei'（微软雅黑）\n", "rcParams['font.sans-serif'] = ['Microsoft YaHei']\n", "rcParams['axes.unicode_minus'] = False  # 解决负号显示问题\n", "\n", "nltk.download('punkt')      # 下载分词器（支持英文句子分词word_tokenize）\n", "nltk.download('wordnet')    # 下载英语词汇数据库（支持词行还原WordNetLemmatizer）\n", "nltk.download('punkt_tab')  # \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 1. 数据加载与预处理"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 398 entries, 0 to 397\n", "Data columns (total 6 columns):\n", " #   Column    Non-Null Count  Dtype \n", "---  ------    --------------  ----- \n", " 0   title     398 non-null    object\n", " 1   authors   398 non-null    object\n", " 2   groups    396 non-null    object\n", " 3   keywords  398 non-null    object\n", " 4   topics    394 non-null    object\n", " 5   abstract  398 non-null    object\n", "dtypes: object(6)\n", "memory usage: 18.8+ KB\n", "None\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "title", "rawType": "object", "type": "string"}, {"name": "authors", "rawType": "object", "type": "string"}, {"name": "groups", "rawType": "object", "type": "string"}, {"name": "keywords", "rawType": "object", "type": "string"}, {"name": "topics", "rawType": "object", "type": "string"}, {"name": "abstract", "rawType": "object", "type": "string"}], "ref": "e112ec11-55ac-437d-95e1-5be226f8d968", "rows": [["0", "Kernelized Bayesian Transfer Learning", "<PERSON><PERSON><PERSON> and <PERSON>", "Novel Machine Learning Algorithms (NMLA)", "cross-domain learning\ndomain adaptation\nkernel methods\ntransfer learning\nvariational approximation", "APP: Biomedical / Bioinformatics\nNMLA: Bayesian Learning\nNMLA: Kernel Methods\nNMLA: Transfer, Adaptation, Multitask Learning\nVIS: Object Recognition", "Transfer learning considers related but distinct tasks defined on heterogenous domains and tries to transfer knowledge between these tasks to improve generalization performance. It is particularly useful when we do not have sufficient amount of labeled training data in some tasks, which may be very costly, laborious, or even infeasible to obtain. Instead, learning the tasks jointly enables us to effectively increase the amount of labeled training data. In this paper, we formulate a kernelized Bayesian transfer learning framework that is a principled combination of kernel-based dimensionality reduction models with task-specific projection matrices to find a shared subspace and a coupled classification model for all of the tasks in this subspace. Our two main contributions are: (i) two novel probabilistic models for binary and multiclass classification, and (ii) very efficient variational approximation procedures for these models. We illustrate the generalization performance of our algorithms on two different applications. In computer vision experiments, our method outperforms the state-of-the-art algorithms on nine out of 12 benchmark supervised domain adaptation experiments defined on two object recognition data sets. In cancer biology experiments, we use our algorithm to predict mutation status of important cancer genes from gene expression profiles using two distinct cancer populations, namely, patient-derived primary tumor data and in-vitro-derived cancer cell line data. We show that we can increase our generalization performance on primary tumors using cell lines as an auxiliary data source."], ["1", "\"Source Free\" Transfer Learning for Text Classification", "<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>", "AI and the Web (AIW)\nNovel Machine Learning Algorithms (NMLA)", "Transfer Learning\nAuxiliary Data Retrieval\nText Classification", "AIW: Knowledge acquisition from the web\nAIW: Machine learning and the web\nNMLA: Transfer, Adaptation, Multitask Learning", "Transfer learning uses relevant auxiliary data to help the learning task in a target domain where labeled data are usually insufficient to train an accurate model. Given appropriate auxiliary data, researchers have proposed many transfer learning models. How to find such auxiliary data, however, is of little research in the past. In this paper, we focus on this auxiliary data retrieval problem, and propose a transfer learning framework that effectively selects helpful auxiliary data from an open knowledge space (e.g. the World Wide Web). Because there is no need of manually selecting auxiliary data for different target domain tasks, we call our framework Source Free Transfer Learning (SFTL). For each target domain task, SFTL framework iteratively queries for the helpful auxiliary data based on the learned model and then updates the model using the retrieved auxiliary data. We highlight the automatic constructions of queries and the robustness of the SFTL framework. Our experiments on the 20 NewsGroup dataset and the Google search snippets dataset suggest that the new framework is capable to have the comparable performance to those state-of-the-art methods with dedicated selections of auxiliary data."], ["2", "A Generalization of Probabilistic Serial to Randomized Social Choice", "<PERSON><PERSON> and <PERSON>", "Game Theory and Economic Paradigms (GTEP)", "social choice theory\nvoting\nfair division\nsocial decision schemes", "GTEP: Game Theory\nGTEP: Social Choice / Voting", "The probabilistic serial (PS) rule is one of the most well-established and desirable rules for the random assignment problem. We present the egalitarian simultaneous reservation (ESR) social decision scheme — an extension of PS to the more general setting of randomized social choice. ESR also generalizes an egalitarian rule from the literature which is defined only for dichotomous preferences. We consider various desirable fairness, efficiency, and strategic properties of ESR and show that it compares favourably against other social decision schemes. Finally, we define a more general class of social decision schemes called Simultaneous Reservation (SR), that contains ESR as well as the serial dictatorship rules. We show that outcomes of SR characterize efficiency with respect to a natural refinement of stochastic dominance."], ["3", "Lifetime Lexical Variation in Social Media", "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>", "NLP and Text Mining (NLPTM)", "Generative model\nSocial Networks\nAge Prediction", "AIW: Web personalization and user modeling\nNLPTM: Information Extraction\nNLPTM: Natural Language Processing (General/Other)", "As the rapid growth of online social media attracts a large number of Internet users, the large volume of content generated by these users also provides us with an opportunity to study the lexical variations of people of different age. In this paper, we present a latent variable model that jointly models the lexical content of tweets and Twitter users' age. Our model inherently assumes that a topic has not only a word distribution but also an age distribution. We propose a Gibbs-EM algorithm to perform inference on our model. Empirical evaluation shows that our model can generate meaningful age-specific topics such as \"school\" for teenagers and \"health\" for older people. Our model also performs age prediction better than a number of baseline methods."], ["4", "Hybrid Singular Value Thresholding for Tensor Completion", "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON>", "Knowledge Representation and Reasoning (KRR)\nMachine Learning Applications (MLA)\nNovel Machine Learning Algorithms (NMLA)\nVision (VIS)", "tensor completion\nlow-rank recovery\nhybrid singular value thresholding", "KRR: Knowledge Representation (General/Other)\nMLA: Machine Learning Applications (General/other)\nNMLA: Data Mining and Knowledge Discovery\nNMLA: Dimension Reduction/Feature Selection\nVIS: Statistical Methods and Learning", "In this paper, we study the low-rank tensor completion problem, where a high-order tensor with missing entries is given and the goal is to complete the tensor. We propose to minimize a new convex objective function, based on log sum of exponentials of nuclear norms, that promotes the low-rankness of unfolding matrices of the completed tensor. We show for the first time that the proximal operator to this objective function is readily computable through a hybrid singular value thresholding scheme. This leads to a new solution to high-order (low-rank) tensor completion via convex relaxation. We show that this convex relaxation and the resulting solution are much more effective than existing tensor completion methods\n(including those also based on minimizing ranks of unfolding matrices). The hybrid singular value thresholding scheme can be applied to any problem where the goal is\nto minimize the maximum rank of a set of low-rank matrices."]], "shape": {"columns": 6, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>title</th>\n", "      <th>authors</th>\n", "      <th>groups</th>\n", "      <th>keywords</th>\n", "      <th>topics</th>\n", "      <th>abstract</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Kernelized Bayesian Transfer Learning</td>\n", "      <td><PERSON><PERSON><PERSON> and <PERSON></td>\n", "      <td>Novel Machine Learning Algorithms (NMLA)</td>\n", "      <td>cross-domain learning\\ndomain adaptation\\nkern...</td>\n", "      <td>APP: Biomedical / Bioinformatics\\nNMLA: Bayesi...</td>\n", "      <td>Transfer learning considers related but distin...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>\"Source Free\" Transfer Learning for Text Class...</td>\n", "      <td><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>...</td>\n", "      <td>AI and the Web (AIW)\\nNovel Machine Learning A...</td>\n", "      <td>Transfer Learning\\nAuxiliary Data Retrieval\\nT...</td>\n", "      <td>AIW: Knowledge acquisition from the web\\nAIW: ...</td>\n", "      <td>Transfer learning uses relevant auxiliary data...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A Generalization of Probabilistic Serial to Ra...</td>\n", "      <td><PERSON><PERSON> and <PERSON></td>\n", "      <td>Game Theory and Economic Paradigms (GTEP)</td>\n", "      <td>social choice theory\\nvoting\\nfair division\\ns...</td>\n", "      <td>GTEP: Game Theory\\nGTEP: Social Choice / Voting</td>\n", "      <td>The probabilistic serial (PS) rule is one of t...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Lifetime Lexical Variation in Social Media</td>\n", "      <td><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> ...</td>\n", "      <td>NLP and Text Mining (NLPTM)</td>\n", "      <td>Generative model\\nSocial Networks\\nAge Prediction</td>\n", "      <td>AIW: Web personalization and user modeling\\nNL...</td>\n", "      <td>As the rapid growth of online social media att...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Hybrid Singular Value Thresholding for Tensor ...</td>\n", "      <td><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON></td>\n", "      <td>Knowledge Representation and Reasoning (KRR)\\n...</td>\n", "      <td>tensor completion\\nlow-rank recovery\\nhybrid s...</td>\n", "      <td>KRR: Knowledge Representation (General/Other)\\...</td>\n", "      <td>In this paper, we study the low-rank tensor co...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                               title  \\\n", "0              Kernelized Bayesian Transfer Learning   \n", "1  \"Source Free\" Transfer Learning for Text Class...   \n", "2  A Generalization of Probabilistic Serial to Ra...   \n", "3         Lifetime Lexical Variation in Social Media   \n", "4  Hybrid Singular Value Thresholding for Tensor ...   \n", "\n", "                                             authors  \\\n", "0                  <PERSON><PERSON><PERSON> and <PERSON>   \n", "1  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Yu...   \n", "2                      <PERSON><PERSON> and <PERSON>   \n", "3  <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> ...   \n", "4   <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON>   \n", "\n", "                                              groups  \\\n", "0           Novel Machine Learning Algorithms (NMLA)   \n", "1  AI and the Web (AIW)\\nNovel Machine Learning A...   \n", "2          Game Theory and Economic Paradigms (GTEP)   \n", "3                        NLP and Text Mining (NLPTM)   \n", "4  Knowledge Representation and Reasoning (KRR)\\n...   \n", "\n", "                                            keywords  \\\n", "0  cross-domain learning\\ndomain adaptation\\nkern...   \n", "1  Transfer Learning\\nAuxiliary Data Retrieval\\nT...   \n", "2  social choice theory\\nvoting\\nfair division\\ns...   \n", "3  Generative model\\nSocial Networks\\nAge Prediction   \n", "4  tensor completion\\nlow-rank recovery\\nhybrid s...   \n", "\n", "                                              topics  \\\n", "0  APP: Biomedical / Bioinformatics\\nNMLA: Bayesi...   \n", "1  AIW: Knowledge acquisition from the web\\nAIW: ...   \n", "2    GTEP: Game Theory\\nGTEP: Social Choice / Voting   \n", "3  AIW: Web personalization and user modeling\\nNL...   \n", "4  KRR: Knowledge Representation (General/Other)\\...   \n", "\n", "                                            abstract  \n", "0  Transfer learning considers related but distin...  \n", "1  Transfer learning uses relevant auxiliary data...  \n", "2  The probabilistic serial (PS) rule is one of t...  \n", "3  As the rapid growth of online social media att...  \n", "4  In this paper, we study the low-rank tensor co...  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# 读取数据\n", "papers = pd.read_csv('data/[UCI] AAAI-14 Accepted Papers - Papers.csv')\n", "# 打印信息\n", "print(papers.info())\n", "papers.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["定义文本预处理函数，包括移除非字母数字字符、转换为小写、分词和词形还原。"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "text", "rawType": "object", "type": "string"}, {"name": "processed_text", "rawType": "object", "type": "string"}], "ref": "3b9e47e0-f954-43bd-a0e8-83fe2fc74ae0", "rows": [["0", "Kernelized Bayesian Transfer Learning cross-domain learning\ndomain adaptation\nkernel methods\ntransfer learning\nvariational approximation Transfer learning considers related but distinct tasks defined on heterogenous domains and tries to transfer knowledge between these tasks to improve generalization performance. It is particularly useful when we do not have sufficient amount of labeled training data in some tasks, which may be very costly, laborious, or even infeasible to obtain. Instead, learning the tasks jointly enables us to effectively increase the amount of labeled training data. In this paper, we formulate a kernelized Bayesian transfer learning framework that is a principled combination of kernel-based dimensionality reduction models with task-specific projection matrices to find a shared subspace and a coupled classification model for all of the tasks in this subspace. Our two main contributions are: (i) two novel probabilistic models for binary and multiclass classification, and (ii) very efficient variational approximation procedures for these models. We illustrate the generalization performance of our algorithms on two different applications. In computer vision experiments, our method outperforms the state-of-the-art algorithms on nine out of 12 benchmark supervised domain adaptation experiments defined on two object recognition data sets. In cancer biology experiments, we use our algorithm to predict mutation status of important cancer genes from gene expression profiles using two distinct cancer populations, namely, patient-derived primary tumor data and in-vitro-derived cancer cell line data. We show that we can increase our generalization performance on primary tumors using cell lines as an auxiliary data source.", "kernelized bayesian transfer learning cross domain learning domain adaptation kernel method transfer learning variational approximation transfer learning considers related but distinct task defined on heterogenous domain and try to transfer knowledge between these task to improve generalization performance it is particularly useful when we do not have sufficient amount of labeled training data in some task which may be very costly laborious or even infeasible to obtain instead learning the task jointly enables u to effectively increase the amount of labeled training data in this paper we formulate a kernelized bayesian transfer learning framework that is a principled combination of kernel based dimensionality reduction model with task specific projection matrix to find a shared subspace and a coupled classification model for all of the task in this subspace our two main contribution are i two novel probabilistic model for binary and multiclass classification and ii very efficient variational approximation procedure for these model we illustrate the generalization performance of our algorithm on two different application in computer vision experiment our method outperforms the state of the art algorithm on nine out of 12 benchmark supervised domain adaptation experiment defined on two object recognition data set in cancer biology experiment we use our algorithm to predict mutation status of important cancer gene from gene expression profile using two distinct cancer population namely patient derived primary tumor data and in vitro derived cancer cell line data we show that we can increase our generalization performance on primary tumor using cell line a an auxiliary data source"], ["1", "\"Source Free\" Transfer Learning for Text Classification Transfer Learning\nAuxiliary Data Retrieval\nText Classification Transfer learning uses relevant auxiliary data to help the learning task in a target domain where labeled data are usually insufficient to train an accurate model. Given appropriate auxiliary data, researchers have proposed many transfer learning models. How to find such auxiliary data, however, is of little research in the past. In this paper, we focus on this auxiliary data retrieval problem, and propose a transfer learning framework that effectively selects helpful auxiliary data from an open knowledge space (e.g. the World Wide Web). Because there is no need of manually selecting auxiliary data for different target domain tasks, we call our framework Source Free Transfer Learning (SFTL). For each target domain task, SFTL framework iteratively queries for the helpful auxiliary data based on the learned model and then updates the model using the retrieved auxiliary data. We highlight the automatic constructions of queries and the robustness of the SFTL framework. Our experiments on the 20 NewsGroup dataset and the Google search snippets dataset suggest that the new framework is capable to have the comparable performance to those state-of-the-art methods with dedicated selections of auxiliary data.", "source free transfer learning for text classification transfer learning auxiliary data retrieval text classification transfer learning us relevant auxiliary data to help the learning task in a target domain where labeled data are usually insufficient to train an accurate model given appropriate auxiliary data researcher have proposed many transfer learning model how to find such auxiliary data however is of little research in the past in this paper we focus on this auxiliary data retrieval problem and propose a transfer learning framework that effectively selects helpful auxiliary data from an open knowledge space e g the world wide web because there is no need of manually selecting auxiliary data for different target domain task we call our framework source free transfer learning sftl for each target domain task sftl framework iteratively query for the helpful auxiliary data based on the learned model and then update the model using the retrieved auxiliary data we highlight the automatic construction of query and the robustness of the sftl framework our experiment on the 20 newsgroup dataset and the google search snippet dataset suggest that the new framework is capable to have the comparable performance to those state of the art method with dedicated selection of auxiliary data"], ["2", "A Generalization of Probabilistic Serial to Randomized Social Choice social choice theory\nvoting\nfair division\nsocial decision schemes The probabilistic serial (PS) rule is one of the most well-established and desirable rules for the random assignment problem. We present the egalitarian simultaneous reservation (ESR) social decision scheme — an extension of PS to the more general setting of randomized social choice. ESR also generalizes an egalitarian rule from the literature which is defined only for dichotomous preferences. We consider various desirable fairness, efficiency, and strategic properties of ESR and show that it compares favourably against other social decision schemes. Finally, we define a more general class of social decision schemes called Simultaneous Reservation (SR), that contains ESR as well as the serial dictatorship rules. We show that outcomes of SR characterize efficiency with respect to a natural refinement of stochastic dominance.", "a generalization of probabilistic serial to randomized social choice social choice theory voting fair division social decision scheme the probabilistic serial p rule is one of the most well established and desirable rule for the random assignment problem we present the egalitarian simultaneous reservation esr social decision scheme an extension of p to the more general setting of randomized social choice esr also generalizes an egalitarian rule from the literature which is defined only for dichotomous preference we consider various desirable fairness efficiency and strategic property of esr and show that it compare favourably against other social decision scheme finally we define a more general class of social decision scheme called simultaneous reservation sr that contains esr a well a the serial dictatorship rule we show that outcome of sr characterize efficiency with respect to a natural refinement of stochastic dominance"], ["3", "Lifetime Lexical Variation in Social Media Generative model\nSocial Networks\nAge Prediction As the rapid growth of online social media attracts a large number of Internet users, the large volume of content generated by these users also provides us with an opportunity to study the lexical variations of people of different age. In this paper, we present a latent variable model that jointly models the lexical content of tweets and Twitter users' age. Our model inherently assumes that a topic has not only a word distribution but also an age distribution. We propose a Gibbs-EM algorithm to perform inference on our model. Empirical evaluation shows that our model can generate meaningful age-specific topics such as \"school\" for teenagers and \"health\" for older people. Our model also performs age prediction better than a number of baseline methods.", "lifetime lexical variation in social medium generative model social network age prediction a the rapid growth of online social medium attracts a large number of internet user the large volume of content generated by these user also provides u with an opportunity to study the lexical variation of people of different age in this paper we present a latent variable model that jointly model the lexical content of tweet and twitter user age our model inherently assumes that a topic ha not only a word distribution but also an age distribution we propose a gibbs em algorithm to perform inference on our model empirical evaluation show that our model can generate meaningful age specific topic such a school for teenager and health for older people our model also performs age prediction better than a number of baseline method"], ["4", "Hybrid Singular Value Thresholding for Tensor Completion tensor completion\nlow-rank recovery\nhybrid singular value thresholding In this paper, we study the low-rank tensor completion problem, where a high-order tensor with missing entries is given and the goal is to complete the tensor. We propose to minimize a new convex objective function, based on log sum of exponentials of nuclear norms, that promotes the low-rankness of unfolding matrices of the completed tensor. We show for the first time that the proximal operator to this objective function is readily computable through a hybrid singular value thresholding scheme. This leads to a new solution to high-order (low-rank) tensor completion via convex relaxation. We show that this convex relaxation and the resulting solution are much more effective than existing tensor completion methods\n(including those also based on minimizing ranks of unfolding matrices). The hybrid singular value thresholding scheme can be applied to any problem where the goal is\nto minimize the maximum rank of a set of low-rank matrices.", "hybrid singular value thresholding for tensor completion tensor completion low rank recovery hybrid singular value thresholding in this paper we study the low rank tensor completion problem where a high order tensor with missing entry is given and the goal is to complete the tensor we propose to minimize a new convex objective function based on log sum of exponential of nuclear norm that promotes the low rankness of unfolding matrix of the completed tensor we show for the first time that the proximal operator to this objective function is readily computable through a hybrid singular value thresholding scheme this lead to a new solution to high order low rank tensor completion via convex relaxation we show that this convex relaxation and the resulting solution are much more effective than existing tensor completion method including those also based on minimizing rank of unfolding matrix the hybrid singular value thresholding scheme can be applied to any problem where the goal is to minimize the maximum rank of a set of low rank matrix"]], "shape": {"columns": 2, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>text</th>\n", "      <th>processed_text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Kernelized Bayesian Transfer Learning cross-do...</td>\n", "      <td>kernelized bayesian transfer learning cross do...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>\"Source Free\" Transfer Learning for Text Class...</td>\n", "      <td>source free transfer learning for text classif...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A Generalization of Probabilistic Serial to Ra...</td>\n", "      <td>a generalization of probabilistic serial to ra...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Lifetime Lexical Variation in Social Media Gen...</td>\n", "      <td>lifetime lexical variation in social medium ge...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Hybrid Singular Value Thresholding for Tensor ...</td>\n", "      <td>hybrid singular value thresholding for tensor ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                text  \\\n", "0  Kernelized Bayesian Transfer Learning cross-do...   \n", "1  \"Source Free\" Transfer Learning for Text Class...   \n", "2  A Generalization of Probabilistic Serial to Ra...   \n", "3  Lifetime Lexical Variation in Social Media Gen...   \n", "4  Hybrid Singular Value Thresholding for Tensor ...   \n", "\n", "                                      processed_text  \n", "0  kernelized bayesian transfer learning cross do...  \n", "1  source free transfer learning for text classif...  \n", "2  a generalization of probabilistic serial to ra...  \n", "3  lifetime lexical variation in social medium ge...  \n", "4  hybrid singular value thresholding for tensor ...  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["from nltk.stem import WordNetLemmatizer\n", "from nltk.tokenize import word_tokenize\n", "\n", "# 提取标题、关键词和摘要合并为一个字段，方便后续处理。\n", "papers['text'] = papers['title'] + ' ' + papers['keywords'] + ' ' + papers['abstract']\n", "papers['text'].head()\n", "\n", "lemmatizer = WordNetLemmatizer()\n", "\n", "def preprocess(text):\n", "    # 移除非字母数字字符\n", "    text = re.sub(r'\\W', ' ', text)\n", "    # 转换为小写\n", "    text = text.lower()\n", "    # 分词\n", "    tokens = word_tokenize(text)\n", "    # 词形还原\n", "    lemmas = [lemmatizer.lemmatize(token) for token in tokens]\n", "    return \" \".join(lemmas)\n", "\n", "papers['processed_text'] = papers['text'].apply(preprocess)\n", "papers[['text', 'processed_text']].head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. 文本向量化 (TF-IDF)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TF-IDF matrix shape: (398, 5038)\n"]}], "source": ["from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "# 创建TF-IDF向量化器\n", "# 使用`sklearn`的`TfidfVectorizer`来创建TF-IDF矩阵，并设置`stop_words='english'`来移除常见的英文停用词（如 \"a\", \"the\", \"is\"），同时通过`max_features=1000`限制了特征向量的维度为1000，只保留最重要的1000个词。\n", "vectorizer = TfidfVectorizer(stop_words='english', max_features=1000)\n", "\n", "# 拟合并转换数据\n", "tfidf_matrix = vectorizer.fit_transform(papers['processed_text'])\n", "\n", "print(f'TF-IDF matrix shape: {tfidf_matrix.shape}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 3. K-<PERSON>s 聚类"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["cluster\n", "4    65\n", "0    61\n", "1    61\n", "5    46\n", "2    34\n", "6    32\n", "8    28\n", "3    28\n", "7    23\n", "9    20\n", "Name: count, dtype: int64\n"]}], "source": ["from sklearn.cluster import KMeans\n", "\n", "num_clusters = 10\n", "kmeans = KMeans(n_clusters=num_clusters, random_state=42, n_init=10)\n", "kmeans.fit(tfidf_matrix)\n", "\n", "papers['cluster'] = kmeans.labels_\n", "\n", "# 查看每个簇的论文数量\n", "print(papers['cluster'].value_counts())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["观察每个簇的中心词，以了解每个簇的主题。"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top terms per cluster:\n", "Cluster 0: learning data task classification transfer method domain quality knowledge model\n", "Cluster 1: problem algorithm learning function planning stochastic time optimization solution online\n", "Cluster 2: manifold graph learning representation model local method embedding selection structure\n", "Cluster 3: image social user emotion network model topic visual discussion method\n", "Cluster 4: behavior agent model learning algorithm dynamic prediction markov data time\n", "Cluster 5: planning language logic action query qualitative belief robot problem reasoning\n", "Cluster 6: constraint counting model linear algorithm graph search parallel datalog symmetry\n", "Cluster 7: view multi feature label instance learning data selection supervised correlation\n", "Cluster 8: game equilibrium strategy security form theory regret agent nash mechanism\n", "Cluster 9: rule voting preference choice election single scoring social candidate voter\n"]}], "source": ["print(\"Top terms per cluster:\")\n", "order_centroids = kmeans.cluster_centers_.argsort()[:, ::-1]\n", "terms = vectorizer.get_feature_names_out()\n", "for i in range(num_clusters):\n", "    print(f\"Cluster {i}:\", end='')\n", "    for ind in order_centroids[i, :10]:\n", "        print(f' {terms[ind]}', end='')\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 4. 降维与可视化 (t-SNE)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["e:\\MyProjects\\PythonProjects\\venv\\py310-64\\MachineLearning\\lib\\site-packages\\sklearn\\manifold\\_t_sne.py:1164: FutureWarning: 'n_iter' was renamed to 'max_iter' in version 1.5 and will be removed in 1.7.\n", "  warnings.warn(\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sklearn.manifold import TSNE\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# 使用t-SNE进行降维\n", "tsne = TSNE(n_components=2, random_state=42, perplexity=30, n_iter=300)\n", "tsne_results = tsne.fit_transform(tfidf_matrix.toarray())\n", "\n", "# 创建一个包含t-SNE结果和簇标签的DataFrame\n", "tsne_df = pd.DataFrame(tsne_results, columns=['tsne1', 'tsne2'])\n", "tsne_df['cluster'] = papers['cluster']\n", "\n", "# 可视化\n", "plt.figure(figsize=(10, 6))\n", "sns.scatterplot(\n", "    x=\"tsne1\", y=\"tsne2\",\n", "    hue=\"cluster\",\n", "    palette=sns.color_palette(\"hsv\", 10),\n", "    data=tsne_df,\n", "    legend=\"full\",\n", "    alpha=0.8\n", ")\n", "plt.title('K-means聚类结果（t-SNE降维）')\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "MachineLearning", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 4}