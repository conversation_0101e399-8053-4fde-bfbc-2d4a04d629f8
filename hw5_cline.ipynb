

import re
import nltk  
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfTransformer  # tf-idf文本向量化
from sklearn.cluster import KMeans, AgglomerativeClustering   # 取类方法
from sklearn.manifold import TSNE  # tSNE降维
from sklearn.decomposition import PCA  #
from scipy import sparse  # 稀疏矩阵
import seaborn as sns  # 作图
from collections import Counter
from matplotlib import rcParams
# 设置中文字体为 'Microsoft YaHei'（微软雅黑）
rcParams['font.sans-serif'] = ['Microsoft YaHei']
rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

nltk.download('punkt')      # 下载分词器（支持英文句子分词word_tokenize）
nltk.download('wordnet')    # 下载英语词汇数据库（支持词行还原WordNetLemmatizer）
nltk.download('punkt_tab')  # 


# 读取数据
papers = pd.read_csv('data/[UCI] AAAI-14 Accepted Papers - Papers.csv')
# 打印信息
print(papers.info())
papers.head()

from nltk.stem import WordNetLemmatizer
from nltk.tokenize import word_tokenize

# 提取标题、关键词和摘要合并为一个字段，方便后续处理。
papers['text'] = papers['title'] + ' ' + papers['keywords'] + ' ' + papers['abstract']
papers['text'].head()

lemmatizer = WordNetLemmatizer()

def preprocess(text):
    # 移除非字母数字字符
    text = re.sub(r'\W', ' ', text)
    # 转换为小写
    text = text.lower()
    # 分词
    tokens = word_tokenize(text)
    # 词形还原
    lemmas = [lemmatizer.lemmatize(token) for token in tokens]
    return " ".join(lemmas)

papers['processed_text'] = papers['text'].apply(preprocess)
papers[['text', 'processed_text']].head()

from sklearn.feature_extraction.text import TfidfVectorizer

# 创建TF-IDF向量化器
# 使用`sklearn`的`TfidfVectorizer`来创建TF-IDF矩阵，并设置`stop_words='english'`来移除常见的英文停用词（如 "a", "the", "is"），同时通过`max_features=1000`限制了特征向量的维度为1000，只保留最重要的1000个词。
vectorizer = TfidfVectorizer(stop_words='english', max_features=1000)

# 拟合并转换数据
tfidf_matrix = vectorizer.fit_transform(papers['processed_text'])

print(f'TF-IDF matrix shape: {tfidf_matrix.shape}')

from sklearn.cluster import KMeans

num_clusters = 10
kmeans = KMeans(n_clusters=num_clusters, random_state=42, n_init=10)
kmeans.fit(tfidf_matrix)

papers['cluster'] = kmeans.labels_

# 查看每个簇的论文数量
print(papers['cluster'].value_counts())

print("Top terms per cluster:")
order_centroids = kmeans.cluster_centers_.argsort()[:, ::-1]
terms = vectorizer.get_feature_names_out()
for i in range(num_clusters):
    print(f"Cluster {i}:", end='')
    for ind in order_centroids[i, :10]:
        print(f' {terms[ind]}', end='')
    print()

from sklearn.manifold import TSNE
import matplotlib.pyplot as plt
import seaborn as sns

# 使用t-SNE进行降维
tsne = TSNE(n_components=2, random_state=42, perplexity=30, n_iter=300)
tsne_results = tsne.fit_transform(tfidf_matrix.toarray())

# 创建一个包含t-SNE结果和簇标签的DataFrame
tsne_df = pd.DataFrame(tsne_results, columns=['tsne1', 'tsne2'])
tsne_df['cluster'] = papers['cluster']

# 可视化
plt.figure(figsize=(10, 6))
sns.scatterplot(
    x="tsne1", y="tsne2",
    hue="cluster",
    palette=sns.color_palette("hsv", 10),
    data=tsne_df,
    legend="full",
    alpha=0.8
)
plt.title('K-means聚类结果（t-SNE降维）')
plt.show()